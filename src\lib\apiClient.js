import axios from 'axios';
import toast from 'react-hot-toast';

// Create axios instance with default configuration
const apiClient = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
    timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 10000,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
});

// Token management utilities
const tokenManager = {
    getToken: () => localStorage.getItem(import.meta.env.VITE_TOKEN_STORAGE_KEY || 'auth_token'),
    setToken: (token) => localStorage.setItem(import.meta.env.VITE_TOKEN_STORAGE_KEY || 'auth_token', token),
    removeToken: () => localStorage.removeItem(import.meta.env.VITE_TOKEN_STORAGE_KEY || 'auth_token'),

    getRefreshToken: () => localStorage.getItem(import.meta.env.VITE_REFRESH_TOKEN_STORAGE_KEY || 'refresh_token'),
    setRefreshToken: (token) => localStorage.setItem(import.meta.env.VITE_REFRESH_TOKEN_STORAGE_KEY || 'refresh_token', token),
    removeRefreshToken: () => localStorage.removeItem(import.meta.env.VITE_REFRESH_TOKEN_STORAGE_KEY || 'refresh_token'),

    getUserData: () => {
        const userData = localStorage.getItem(import.meta.env.VITE_USER_STORAGE_KEY || 'user_data');
        return userData ? JSON.parse(userData) : null;
    },
    setUserData: (userData) => localStorage.setItem(import.meta.env.VITE_USER_STORAGE_KEY || 'user_data', JSON.stringify(userData)),
    removeUserData: () => localStorage.removeItem(import.meta.env.VITE_USER_STORAGE_KEY || 'user_data'),

    clearAll: () => {
        tokenManager.removeToken();
        tokenManager.removeRefreshToken();
        tokenManager.removeUserData();
    }
};

// Request interceptor to add auth token
apiClient.interceptors.request.use(
    (config) => {
        const token = tokenManager.getToken();
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }

        // Add request timestamp for debugging
        if (import.meta.env.VITE_DEBUG === 'true') {
            console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
                headers: config.headers,
                data: config.data,
                params: config.params,
            });
        }

        return config;
    },
    (error) => {
        console.error('[API Request Error]', error);
        return Promise.reject(error);
    }
);

// Response interceptor for error handling and token refresh
apiClient.interceptors.response.use(
    (response) => {
        // Log successful responses in debug mode
        if (import.meta.env.VITE_DEBUG === 'true') {
            console.log(`[API Response] ${response.config.method?.toUpperCase()} ${response.config.url}`, {
                status: response.status,
                data: response.data,
            });
        }

        return response;
    },
    async (error) => {
        const originalRequest = error.config;

        // Log errors in debug mode
        if (import.meta.env.VITE_DEBUG === 'true') {
            console.error('[API Response Error]', {
                url: error.config?.url,
                status: error.response?.status,
                data: error.response?.data,
                message: error.message,
            });
        }

        // Handle 401 Unauthorized - Token expired or invalid
        if (error.response?.status === 401 && !originalRequest._retry) {
            originalRequest._retry = true;

            const refreshToken = tokenManager.getRefreshToken();

            if (refreshToken) {
                try {
                    // Attempt to refresh the token
                    const response = await axios.post(
                        `${import.meta.env.VITE_API_BASE_URL}/auth/refresh`,
                        { refresh_token: refreshToken }
                    );

                    const { access_token, refresh_token: newRefreshToken } = response.data;

                    // Update stored tokens
                    tokenManager.setToken(access_token);
                    if (newRefreshToken) {
                        tokenManager.setRefreshToken(newRefreshToken);
                    }

                    // Retry the original request with new token
                    originalRequest.headers.Authorization = `Bearer ${access_token}`;
                    return apiClient(originalRequest);

                } catch (refreshError) {
                    // Refresh failed, clear tokens and redirect to login
                    tokenManager.clearAll();
                    toast.error('Session expired. Please login again.');

                    // Redirect to login page
                    if (typeof window !== 'undefined') {
                        const event = new CustomEvent('auth:logout');
                        window.dispatchEvent(event);
                    }

                    return Promise.reject(refreshError);
                }
            } else {
                // No refresh token available, clear storage and redirect
                tokenManager.clearAll();
                toast.error('Please login to continue.');

                if (typeof window !== 'undefined') {
                    const event = new CustomEvent('auth:logout');
                    window.dispatchEvent(event);
                }
            }
        }

        // Handle other HTTP errors
        const errorMessage = error.response?.data?.message ||
            error.response?.data?.error ||
            error.message ||
            'An unexpected error occurred';

        // Don't show toast for certain status codes or if explicitly disabled
        if (!originalRequest.skipErrorToast) {
            switch (error.response?.status) {
                case 400:
                    toast.error(errorMessage);
                    break;
                case 403:
                    toast.error('Access denied. You don\'t have permission to perform this action.');
                    break;
                case 404:
                    toast.error('Resource not found.');
                    break;
                case 422:
                    // Validation errors - handle them in components
                    break;
                case 500:
                    toast.error('Server error. Please try again later.');
                    break;
                default:
                    if (error.response?.status >= 400) {
                        toast.error(errorMessage);
                    }
            }
        }

        return Promise.reject(error);
    }
);

// API methods for authentication
export const authAPI = {
    login: (credentials) => apiClient.post('/auth/login', credentials),
    register: (userData) => apiClient.post('/auth/register', userData),
    logout: () => apiClient.post('/auth/logout'),
    forgotPassword: (email) => apiClient.post('/auth/forgot-password', { email }),
    resetPassword: (token, password, passwordConfirmation) =>
        apiClient.post('/auth/reset-password', {
            token,
            password,
            password_confirmation: passwordConfirmation
        }),
    refreshToken: (refreshToken) => apiClient.post('/auth/refresh', { refresh_token: refreshToken }),
    getProfile: () => apiClient.get('/auth/profile'),
    updateProfile: (userData) => apiClient.put('/auth/profile', userData),
    changePassword: (currentPassword, newPassword, passwordConfirmation) =>
        apiClient.put('/auth/change-password', {
            current_password: currentPassword,
            new_password: newPassword,
            password_confirmation: passwordConfirmation,
        }),
};

// Generic API methods
export const api = {
    get: (url, config = {}) => apiClient.get(url, config),
    post: (url, data = {}, config = {}) => apiClient.post(url, data, config),
    put: (url, data = {}, config = {}) => apiClient.put(url, data, config),
    patch: (url, data = {}, config = {}) => apiClient.patch(url, data, config),
    delete: (url, config = {}) => apiClient.delete(url, config),
};

// Export token manager for use in other parts of the app
export { tokenManager };

// Export the configured axios instance
export default apiClient;
