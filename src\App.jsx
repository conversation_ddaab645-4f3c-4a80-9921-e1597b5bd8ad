import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';

// Context Providers
import { AuthProvider } from './contexts/AuthContext';

// Pages
import Login from './pages/Login';
import Register from './pages/Register';
import ForgotPassword from './pages/ForgotPassword';
import ResetPassword from './pages/ResetPassword';

// Protected Route Component
import ProtectedRoute from './components/ProtectedRoute';

// Placeholder Dashboard Component
const Dashboard = () => {
    return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
            <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
                <h1 className="text-2xl font-bold text-gray-900 mb-4">
                    Welcome to {import.meta.env.VITE_APP_NAME || 'SaaS CRM'}
                </h1>
                <p className="text-gray-600 mb-6">
                    You have successfully logged in to your dashboard.
                </p>
                <p className="text-sm text-gray-500">
                    This is a placeholder dashboard. Replace this component with your actual dashboard content.
                </p>
            </div>
        </div>
    );
};

function App() {
    const basename = import.meta.env.VITE_APP_BASENAME || '';

    return (
        <Router basename={basename}>
            <AuthProvider>
                <div className="App">
                    <Routes>
                        {/* Public Routes */}
                        <Route path="/login" element={<Login />} />

                        {/* Conditional Registration Route */}
                        {import.meta.env.VITE_ENABLE_REGISTRATION === 'true' && (
                            <Route path="/register" element={<Register />} />
                        )}

                        {/* Conditional Forgot Password Routes */}
                        {import.meta.env.VITE_ENABLE_FORGOT_PASSWORD === 'true' && (
                            <>
                                <Route path="/forgot-password" element={<ForgotPassword />} />
                                <Route path="/reset-password" element={<ResetPassword />} />
                            </>
                        )}

                        {/* Protected Routes */}
                        <Route
                            path="/dashboard"
                            element={
                                <ProtectedRoute>
                                    <Dashboard />
                                </ProtectedRoute>
                            }
                        />

                        {/* Default Route */}
                        <Route path="/" element={<Navigate to="/login" replace />} />

                        {/* Catch-all Route */}
                        <Route path="*" element={<Navigate to="/login" replace />} />
                    </Routes>

                    {/* Toast Notifications */}
                    <Toaster
                        position="top-right"
                        toastOptions={{
                            duration: 4000,
                            style: {
                                background: '#363636',
                                color: '#fff',
                            },
                            success: {
                                duration: 3000,
                                theme: {
                                    primary: 'green',
                                    secondary: 'black',
                                },
                            },
                            error: {
                                duration: 5000,
                                theme: {
                                    primary: 'red',
                                    secondary: 'black',
                                },
                            },
                        }}
                    />
                </div>
            </AuthProvider>
        </Router>
    );
}

export default App;
