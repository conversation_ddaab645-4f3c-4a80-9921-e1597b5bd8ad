import { useState } from 'react'
import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'
// import './App.css'
import { Button } from './components/ui/button'
import { Accordion } from '@radix-ui/react-accordion'

function App() {
    const [count, setCount] = useState(0)

    return (
        <div>
            {/* <Button>Click me you bitch</Button>
             */}
            <Accordion.Root>
                <Accordion.Item value="item-1">
                    <Accordion.Trigger>Item 1</Accordion.Trigger>
                    <Accordion.Content>Item 1 content</Accordion.Content>
                </Accordion.Item>
                <Accordion.Item value="item-2">
                    <Accordion.Trigger>Item 2</Accordion.Trigger>
                    <Accordion.Content>Item 2 content</Accordion.Content>
                </Accordion.Item>
            </Accordion.Root>
        </div>
    )
}

export default App
